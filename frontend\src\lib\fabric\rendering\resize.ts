import { Canvas } from "fabric";
import { CropData } from "@/shared/types";
import { applyUniformScalingToCanvas } from "../operations/uniformScaling";

// Calculate target dimensions that fit within container while maintaining aspect ratio
export const calculateFittedCanvasDimensions = (
  contentWidth: number,
  contentHeight: number,
  containerWidth: number,
  containerHeight: number
) => {
  const aspectRatio = contentWidth / contentHeight;
  let targetWidth = containerWidth;
  let targetHeight = targetWidth / aspectRatio;

  if (targetHeight > containerHeight) {
    targetHeight = containerHeight;
    targetWidth = targetHeight * aspectRatio;
  }

  return { width: targetWidth, height: targetHeight };
};

// Check if resize is needed based on threshold
export const shouldResize = (
  currentWidth: number,
  currentHeight: number,
  targetWidth: number,
  targetHeight: number,
  threshold: number = 5
): boolean => {
  return (
    Math.abs(currentWidth - targetWidth) >= threshold ||
    Math.abs(currentHeight - targetHeight) >= threshold
  );
};

// Apply minimum canvas dimensions
export const applyMinimumDimensions = (width: number, height: number) => {
  return {
    width: Math.max(width, 300),
    height: Math.max(height, 200),
  };
};

// Scale and position objects during canvas resize
export const scaleCanvasObjects = (canvas: Canvas, imageScale: number) => {
  // First, scale and position all objects
  canvas.forEachObject((obj) => {
    const objName = (obj as unknown as Record<string, unknown>)?.name;
    if (objName !== "backgroundImage") {
      obj.set({
        scaleX: (obj.scaleX || 1) * imageScale,
        scaleY: (obj.scaleY || 1) * imageScale,
        left: (obj.left || 0) * imageScale,
        top: (obj.top || 0) * imageScale,
      });
      obj.setCoords();
    }
  });

  // Then apply uniform scaling to handle stroke widths and text consistently
  applyUniformScalingToCanvas(canvas, Math.min(imageScale, imageScale));
};

// Position background image based on rotation
export const positionBackgroundImage = (
  bgImg: any,
  actualWidth: number,
  actualHeight: number,
  imageScale: number
) => {
  const currentAngle = bgImg.angle || 0;
  const currentFlipX = bgImg.flipX || false;
  const currentFlipY = bgImg.flipY || false;

  bgImg.scaleX = (bgImg.scaleX || 1) * imageScale;
  bgImg.scaleY = (bgImg.scaleY || 1) * imageScale;

  if ([0, 180].includes(currentAngle)) {
    bgImg.left = 0;
    bgImg.top = 0;
  } else {
    bgImg.set({
      left: actualWidth / 2,
      top: actualHeight / 2,
      originX: "center",
      originY: "center",
    });
  }

  bgImg.angle = currentAngle;
  bgImg.flipX = currentFlipX;
  bgImg.flipY = currentFlipY;
};

// Update crop data with new container dimensions
export const updateCropDataDimensions = (
  cropData: CropData,
  containerWidth: number,
  containerHeight: number
): CropData => {
  return {
    ...cropData,
    canvasDimensions: {
      width: containerWidth,
      height: containerHeight,
    },
  };
};
