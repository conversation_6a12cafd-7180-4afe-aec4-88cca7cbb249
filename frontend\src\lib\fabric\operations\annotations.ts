import { Canvas, FabricImage, Textbox } from "fabric";

let originalAnnotationState: unknown = null;

let currentCropScale: number = 1;

export const setCropScale = (scale: number): void => {
  currentCropScale = scale;
};

export const getCropScale = (): number => {
  return currentCropScale;
};

export const clearCropScale = (): void => {
  currentCropScale = 1;
};

export const saveAnnotationsForCrop = (canvas: Canvas): unknown => {
  const currentState = canvas.toObject(["name", "id"]);
  if (!originalAnnotationState) {
    originalAnnotationState = JSON.parse(JSON.stringify(currentState));
  }

  return currentState;
};

export const getViewportScale = (canvas: Canvas): number => {
  const vpt = canvas.viewportTransform;
  if (!vpt) return 1;
  return vpt[0];
};

export const adjustStrokeWidthForViewport = (strokeWidth: number, scale?: number): number => {
  const scaleToUse = scale !== undefined ? scale : currentCropScale;
  return strokeWidth / scaleToUse;
};

export const adjustNewShapeStrokeWidth = (shape: any, scale?: number): void => {
  if (!shape || !("strokeWidth" in shape) || !shape.strokeWidth) return;

  const scaleToUse = scale !== undefined ? scale : currentCropScale;
  if (scaleToUse !== 1) {
    shape.set({ strokeWidth: shape.strokeWidth / scaleToUse });
  }
};

export const adjustAnnotationsForCrop = (canvas: Canvas, scaleX: number, scaleY: number): void => {
  if (!originalAnnotationState) {
    originalAnnotationState = canvas.toObject(["name", "id"]);
  }

  const uniformScale = Math.min(scaleX, scaleY);
  setCropScale(uniformScale);

  canvas.forEachObject((obj) => {
    const objName = (obj as any).name;
    if (objName === "backgroundImage" || objName === "cropRect") {
      return;
    }

    if ("strokeWidth" in obj && obj.strokeWidth) {
      const newStrokeWidth = obj.strokeWidth / uniformScale;
      obj.set({ strokeWidth: newStrokeWidth });
    }

    if (obj.type === "textbox" || obj.type === "text") {
      const currentScaleX = obj.scaleX || 1;
      const currentScaleY = obj.scaleY || 1;
      const textScaleX = currentScaleX / scaleX;
      const textScaleY = currentScaleY / scaleY;
      obj.set({
        scaleX: textScaleX,
        scaleY: textScaleY,
      });
    }

    obj.setCoords();
  });
};

export const restoreAnnotationsToOriginalState = async (canvas: Canvas): Promise<void> => {
  if (!originalAnnotationState) return;
  const objectsToRemove = canvas.getObjects().filter((obj) => {
    const objName = (obj as any).name;
    return objName !== "backgroundImage";
  });
  objectsToRemove.forEach((obj) => canvas.remove(obj));
  await loadAnnotations(canvas, originalAnnotationState);
};

export const clearOriginalAnnotationState = (): void => {
  originalAnnotationState = null;
};

export const restoreAnnotationsAfterUncrop = async (
  canvas: Canvas,
  savedAnnotations: unknown
): Promise<void> => {
  // Get current annotations (including any drawn while cropped)
  const currentAnnotations = canvas.toObject(["name", "id"]);

  // Merge original annotations with any new ones added during crop
  let annotationsToRestore = originalAnnotationState;

  if (originalAnnotationState && currentAnnotations) {
    const originalData = originalAnnotationState as any;
    const currentData = currentAnnotations as any;

    // Find annotations that were added during crop (not in original state)
    const originalIds = new Set(
      originalData.objects?.map((obj: any) => obj.id || `${obj.left}_${obj.top}_${obj.type}`) || []
    );

    const newAnnotations =
      currentData.objects?.filter((obj: any) => {
        const objId = obj.id || `${obj.left}_${obj.top}_${obj.type}`;
        return !originalIds.has(objId) && obj.name !== "backgroundImage" && obj.name !== "cropRect";
      }) || [];

    // Merge original and new annotations
    if (newAnnotations.length > 0) {
      annotationsToRestore = {
        ...originalData,
        objects: [...(originalData.objects || []), ...newAnnotations],
      };
    }
  }

  // Restore the merged annotations
  if (annotationsToRestore) {
    const objectsToRemove = canvas.getObjects().filter((obj) => {
      const objName = (obj as any).name;
      return objName !== "backgroundImage";
    });
    objectsToRemove.forEach((obj) => canvas.remove(obj));
    await loadAnnotations(canvas, annotationsToRestore);
  } else if (savedAnnotations) {
    const objectsToRemove = canvas.getObjects().filter((obj) => {
      const objName = (obj as any).name;
      return objName !== "backgroundImage";
    });
    objectsToRemove.forEach((obj) => canvas.remove(obj));
    await loadAnnotations(canvas, savedAnnotations);
  }

  // Clear the original state since we're uncropping
  clearOriginalAnnotationState();
  // Clear the crop scale since we're uncropping
  clearCropScale();
};

export const loadAnnotations = async (canvas: Canvas, annotations: unknown): Promise<void> => {
  if (!annotations || !canvas) return;
  const backgroundImage = canvas.backgroundImage as FabricImage | null;
  const currentWidth = canvas.getWidth();
  const currentHeight = canvas.getHeight();

  const annotationsData = annotations as {
    canvasWidth?: number;
    canvasHeight?: number;
    objects?: unknown[];
    [key: string]: unknown;
  };

  await canvas.loadFromJSON(annotations);

  if (backgroundImage) {
    canvas.backgroundImage = backgroundImage;
  }

  if (annotationsData.canvasWidth && annotationsData.canvasHeight) {
    const savedWidth = annotationsData.canvasWidth;
    const savedHeight = annotationsData.canvasHeight;

    const scaleX = currentWidth / savedWidth;
    const scaleY = currentHeight / savedHeight;

    canvas.forEachObject((obj) => {
      const objName = (obj as unknown as Record<string, unknown>)?.name;
      if (objName !== "backgroundImage") {
        obj.scaleX = (obj.scaleX || 1) * scaleX;
        obj.scaleY = (obj.scaleY || 1) * scaleY;
        obj.left = (obj.left || 0) * scaleX;
        obj.top = (obj.top || 0) * scaleY;

        if ("strokeUniform" in obj) {
          obj.strokeUniform = true;
        }
        if (obj.type === "textbox" || obj.type === "text") {
          const textbox = obj as Textbox;
          textbox.fontSize = 16;
          textbox.set({
            scaleX: 1,
            scaleY: 1,
            lockScalingX: true,
            lockScalingY: true,
            hasControls: false,
          });
        }
        obj.selectable = false;
        obj.evented = false;
        obj.setCoords();
      }
    });
  }

  const lines = canvas.getObjects().filter((obj: any) => obj.name === "measurementLine");
  const texts = canvas.getObjects().filter((obj: any) => obj.name === "measurementText");

  lines.forEach((line: any) => {
    const x1 = line.left + line.x1 * line.scaleX;
    const y1 = line.top + line.y1 * line.scaleY;
    const x2 = line.left + line.x2 * line.scaleX;
    const y2 = line.top + line.y2 * line.scaleY;
    const midX = (x1 + x2) / 2;
    const midY = (y1 + y2) / 2;
    const matchingText = texts.find((text: any) => {
      const dx = Math.abs((text.left ?? 0) - midX);
      const dy = Math.abs((text.top ?? 0) - midY);
      return dx < 20 && dy < 20;
    });

    if (matchingText) {
      line.measurementText = matchingText;
    }
  });

  canvas.forEachObject((obj) => {
    const objName = (obj as unknown as Record<string, unknown>)?.name;
    if (objName !== "backgroundImage") {
      obj.selectable = false;
      obj.evented = false;
    }
  });

  canvas.calcOffset();
  canvas.renderAll();
};
