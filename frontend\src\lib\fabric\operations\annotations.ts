import { Canvas, FabricImage, Textbox } from "fabric";

// Store to keep track of original annotation state before any crop adjustments
let originalAnnotationState: unknown = null;

export const saveAnnotationsForCrop = (canvas: Canvas): unknown => {
  // Save the current state before any crop adjustments are applied
  const currentState = canvas.toObject(["name", "id"]);

  // Store original state only if we haven't stored it yet (first crop)
  if (!originalAnnotationState) {
    originalAnnotationState = JSON.parse(JSON.stringify(currentState));
  }

  return currentState;
};

// Get the current viewport scale factor
export const getViewportScale = (canvas: Canvas): number => {
  const vpt = canvas.viewportTransform;
  if (!vpt) return 1;
  // Return the scale factor from the viewport transform matrix
  return vpt[0]; // scaleX (assuming uniform scaling)
};

// Adjust stroke width for new annotations based on current viewport scale
export const adjustStrokeWidthForViewport = (canvas: Canvas, strokeWidth: number): number => {
  const scale = getViewportScale(canvas);
  return strokeWidth / scale;
};

// Adjust stroke width for a newly created shape based on viewport scale
export const adjustNewShapeStrokeWidth = (canvas: Canvas, shape: any): void => {
  if (!shape || !("strokeWidth" in shape) || !shape.strokeWidth) return;

  const scale = getViewportScale(canvas);
  if (scale !== 1) {
    shape.set({ strokeWidth: shape.strokeWidth / scale });
  }
};

export const adjustAnnotationsForCrop = (canvas: Canvas, scaleX: number, scaleY: number): void => {
  // Save original state before making any adjustments
  if (!originalAnnotationState) {
    originalAnnotationState = canvas.toObject(["name", "id"]);
  }

  canvas.forEachObject((obj) => {
    const objName = (obj as any).name;
    if (objName === "backgroundImage" || objName === "cropRect") {
      return;
    }

    if ("strokeWidth" in obj && obj.strokeWidth) {
      const newStrokeWidth = obj.strokeWidth / Math.min(scaleX, scaleY);
      obj.set({ strokeWidth: newStrokeWidth });
    }

    if (obj.type === "textbox" || obj.type === "text") {
      const currentScaleX = obj.scaleX || 1;
      const currentScaleY = obj.scaleY || 1;
      const textScaleX = currentScaleX / scaleX;
      const textScaleY = currentScaleY / scaleY;
      obj.set({
        scaleX: textScaleX,
        scaleY: textScaleY,
      });
    }

    obj.setCoords();
  });
};

// Restore annotations to their original state (before any crop adjustments)
export const restoreAnnotationsToOriginalState = async (canvas: Canvas): Promise<void> => {
  if (!originalAnnotationState) return;

  // Remove all current annotations except background
  const objectsToRemove = canvas.getObjects().filter((obj) => {
    const objName = (obj as any).name;
    return objName !== "backgroundImage";
  });
  objectsToRemove.forEach((obj) => canvas.remove(obj));

  // Restore from the original saved state
  await loadAnnotations(canvas, originalAnnotationState);
};

// Clear the stored original state (call when completely done with crop operations)
export const clearOriginalAnnotationState = (): void => {
  originalAnnotationState = null;
};

export const restoreAnnotationsAfterUncrop = async (
  canvas: Canvas,
  savedAnnotations: unknown
): Promise<void> => {
  // Always restore from original state if available, otherwise use saved annotations
  if (originalAnnotationState) {
    await restoreAnnotationsToOriginalState(canvas);
    // Clear the original state since we're uncropping
    clearOriginalAnnotationState();
  } else if (savedAnnotations) {
    const objectsToRemove = canvas.getObjects().filter((obj) => {
      const objName = (obj as any).name;
      return objName !== "backgroundImage";
    });
    objectsToRemove.forEach((obj) => canvas.remove(obj));
    await loadAnnotations(canvas, savedAnnotations);
  }
};

export const loadAnnotations = async (canvas: Canvas, annotations: unknown): Promise<void> => {
  if (!annotations || !canvas) return;
  const backgroundImage = canvas.backgroundImage as FabricImage | null;
  const currentWidth = canvas.getWidth();
  const currentHeight = canvas.getHeight();

  const annotationsData = annotations as {
    canvasWidth?: number;
    canvasHeight?: number;
    objects?: unknown[];
    [key: string]: unknown;
  };

  await canvas.loadFromJSON(annotations);

  if (backgroundImage) {
    canvas.backgroundImage = backgroundImage;
  }

  if (annotationsData.canvasWidth && annotationsData.canvasHeight) {
    const savedWidth = annotationsData.canvasWidth;
    const savedHeight = annotationsData.canvasHeight;

    const scaleX = currentWidth / savedWidth;
    const scaleY = currentHeight / savedHeight;

    canvas.forEachObject((obj) => {
      const objName = (obj as unknown as Record<string, unknown>)?.name;
      if (objName !== "backgroundImage") {
        obj.scaleX = (obj.scaleX || 1) * scaleX;
        obj.scaleY = (obj.scaleY || 1) * scaleY;
        obj.left = (obj.left || 0) * scaleX;
        obj.top = (obj.top || 0) * scaleY;

        if ("strokeUniform" in obj) {
          obj.strokeUniform = true;
        }
        if (obj.type === "textbox" || obj.type === "text") {
          const textbox = obj as Textbox;
          textbox.fontSize = 16;
          textbox.set({
            scaleX: 1,
            scaleY: 1,
            lockScalingX: true,
            lockScalingY: true,
            hasControls: false,
          });
        }
        obj.selectable = false;
        obj.evented = false;
        obj.setCoords();
      }
    });
  }

  const lines = canvas.getObjects().filter((obj: any) => obj.name === "measurementLine");
  const texts = canvas.getObjects().filter((obj: any) => obj.name === "measurementText");

  lines.forEach((line: any) => {
    const x1 = line.left + line.x1 * line.scaleX;
    const y1 = line.top + line.y1 * line.scaleY;
    const x2 = line.left + line.x2 * line.scaleX;
    const y2 = line.top + line.y2 * line.scaleY;
    const midX = (x1 + x2) / 2;
    const midY = (y1 + y2) / 2;
    const matchingText = texts.find((text: any) => {
      const dx = Math.abs((text.left ?? 0) - midX);
      const dy = Math.abs((text.top ?? 0) - midY);
      return dx < 20 && dy < 20;
    });

    if (matchingText) {
      line.measurementText = matchingText;
    }
  });

  canvas.forEachObject((obj) => {
    const objName = (obj as unknown as Record<string, unknown>)?.name;
    if (objName !== "backgroundImage") {
      obj.selectable = false;
      obj.evented = false;
    }
  });

  canvas.calcOffset();
  canvas.renderAll();
};
