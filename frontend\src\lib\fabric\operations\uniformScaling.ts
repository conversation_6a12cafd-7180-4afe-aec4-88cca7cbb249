import { Canvas, Textbox } from "fabric";

// Store original values for consistent scaling
const originalValues = new Map<
  any,
  {
    strokeWidth?: number;
    fontSize?: number;
    scaleX?: number;
    scaleY?: number;
  }
>();

// Store the current uniform scale factor
let currentUniformScale: number = 1;

/**
 * Set the current uniform scale factor
 */
export const setUniformScale = (scale: number): void => {
  currentUniformScale = scale;
};

/**
 * Get the current uniform scale factor
 */
export const getUniformScale = (): number => {
  return currentUniformScale;
};

/**
 * Clear the uniform scale (reset to 1)
 */
export const clearUniformScale = (): void => {
  currentUniformScale = 1;
  originalValues.clear();
};

/**
 * Store original values for an object before any scaling
 */
export const storeOriginalValues = (obj: any): void => {
  if (originalValues.has(obj)) return; // Already stored

  const values: any = {};

  if ("strokeWidth" in obj && obj.strokeWidth) {
    values.strokeWidth = obj.strokeWidth;
  }

  if (obj.type === "textbox" || obj.type === "text") {
    values.fontSize = obj.fontSize || 16;
    values.scaleX = obj.scaleX || 1;
    values.scaleY = obj.scaleY || 1;
  }

  originalValues.set(obj, values);
};

/**
 * Apply uniform scaling to stroke width
 */
export const applyUniformStrokeWidth = (obj: any, scale?: number): void => {
  if (!("strokeWidth" in obj) || !obj.strokeWidth) return;

  const scaleToUse = scale !== undefined ? scale : currentUniformScale;

  // Store original if not already stored
  storeOriginalValues(obj);

  const original = originalValues.get(obj);
  if (original?.strokeWidth) {
    const newStrokeWidth = original.strokeWidth / scaleToUse;
    console.log(
      `Stroke width scaling: original=${original.strokeWidth}, scale=${scaleToUse}, new=${newStrokeWidth}`
    );
    obj.set({ strokeWidth: newStrokeWidth });
  }
};

/**
 * Apply uniform scaling to text
 */
export const applyUniformTextScaling = (obj: any, scale?: number): void => {
  if (obj.type !== "textbox" && obj.type !== "text") return;

  const scaleToUse = scale !== undefined ? scale : currentUniformScale;

  // Store original if not already stored
  storeOriginalValues(obj);

  const original = originalValues.get(obj);
  if (original) {
    const textbox = obj as Textbox;
    // Calculate font size to appear as 16px visually - divide by scale to compensate for viewport scaling
    const targetFontSize = 16 / scaleToUse;
    console.log(
      `Text scaling: originalFontSize=16, scale=${scaleToUse}, targetFontSize=${targetFontSize}`
    );
    textbox.set({
      fontSize: targetFontSize,
      scaleX: 1, // Don't scale the text object itself
      scaleY: 1, // Don't scale the text object itself
      lockScalingX: true,
      lockScalingY: true,
      hasControls: false,
    });
  }
};

/**
 * Apply uniform scaling to all objects on canvas
 */
export const applyUniformScalingToCanvas = (canvas: Canvas, scale?: number): void => {
  const scaleToUse = scale !== undefined ? scale : currentUniformScale;

  // Update the stored scale
  if (scale !== undefined) {
    setUniformScale(scale);
  }

  canvas.forEachObject((obj) => {
    const objName = (obj as any).name;
    if (objName === "backgroundImage" || objName === "cropRect") {
      return;
    }

    // Ensure strokeUniform is set
    if ("strokeUniform" in obj) {
      obj.strokeUniform = true;
    }

    // Apply uniform stroke width
    applyUniformStrokeWidth(obj, scaleToUse);

    // Apply uniform text scaling
    applyUniformTextScaling(obj, scaleToUse);

    obj.setCoords();
  });
};

/**
 * Restore objects to their original state
 */
export const restoreOriginalScaling = (canvas: Canvas): void => {
  canvas.forEachObject((obj) => {
    const objName = (obj as any).name;
    if (objName === "backgroundImage" || objName === "cropRect") {
      return;
    }

    const original = originalValues.get(obj);
    if (original) {
      const updates: any = {};

      if (original.strokeWidth !== undefined) {
        updates.strokeWidth = original.strokeWidth;
      }

      if (obj.type === "textbox" || obj.type === "text") {
        updates.fontSize = 16; // Always restore to 16px
        updates.scaleX = 1; // Always restore to scale 1
        updates.scaleY = 1; // Always restore to scale 1
      }

      obj.set(updates);
      obj.setCoords();
    }
  });

  // Clear stored values and reset scale
  clearUniformScale();
};

/**
 * Get adjusted stroke width for new objects
 */
export const getAdjustedStrokeWidth = (originalWidth: number, scale?: number): number => {
  const scaleToUse = scale !== undefined ? scale : currentUniformScale;
  return originalWidth / scaleToUse;
};

/**
 * Apply uniform scaling to a newly created object
 */
export const applyUniformScalingToNewObject = (obj: any, scale?: number): void => {
  const scaleToUse = scale !== undefined ? scale : currentUniformScale;

  if (scaleToUse === 1) return; // No scaling needed

  // For new objects, we need to store the original values BEFORE any scaling
  // Store the current values as "original" since this is a new object
  if (!originalValues.has(obj)) {
    const values: any = {};

    if ("strokeWidth" in obj && obj.strokeWidth) {
      values.strokeWidth = obj.strokeWidth;
    }

    if (obj.type === "textbox" || obj.type === "text") {
      values.fontSize = obj.fontSize || 16;
      values.scaleX = obj.scaleX || 1;
      values.scaleY = obj.scaleY || 1;
    }

    originalValues.set(obj, values);
  }

  // Apply uniform scaling
  applyUniformStrokeWidth(obj, scaleToUse);
  applyUniformTextScaling(obj, scaleToUse);

  obj.setCoords();
};
