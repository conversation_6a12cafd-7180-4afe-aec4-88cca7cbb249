import { Canvas } from "fabric";
import { useCallback, useEffect, useRef } from "react";
import { UseResponsiveCanvasProps, CropData } from "@/shared/types";
import {
  calculateFittedCanvasDimensions,
  shouldResize,
  applyMinimumDimensions,
  scaleCanvasObjects,
  positionBackgroundImage,
} from "@/lib/fabric/rendering";

const handleCroppedCanvasResize = (
  canvas: Canvas,
  container: HTMLElement,
  cropData: CropData,
  setCropData?: (data: CropData) => void
) => {
  if (!cropData?.normalizedCropRect || !cropData.canvasDimensions) return;

  const containerRect = container.getBoundingClientRect();
  const currentCanvasWidth = canvas.getWidth();
  const currentCanvasHeight = canvas.getHeight();

  // Calculate target canvas size to fit the crop area aspect ratio
  const cropAspectRatio = cropData.normalizedCropRect.width / cropData.normalizedCropRect.height;
  let targetWidth = containerRect.width;
  let targetHeight = targetWidth / cropAspectRatio;

  if (targetHeight > containerRect.height) {
    targetHeight = containerRect.height;
    targetWidth = targetHeight * cropAspectRatio;
  }

  // Only resize if there's a significant change in target dimensions
  if (!shouldResize(currentCanvasWidth, currentCanvasHeight, targetWidth, targetHeight)) {
    return;
  }

  // Calculate the original crop area in canvas coordinates
  const backgroundImage = canvas.backgroundImage;
  if (!backgroundImage) return;

  const imageWidth = backgroundImage.width || 512;
  const imageHeight = backgroundImage.height || 512;
  const imageScale = backgroundImage.scaleX || 1;
  const scaledImageWidth = imageWidth * imageScale;
  const scaledImageHeight = imageHeight * imageScale;

  // Convert normalized crop rect back to canvas coordinates
  const cropLeft = cropData.normalizedCropRect.left * scaledImageWidth;
  const cropTop = cropData.normalizedCropRect.top * scaledImageHeight;
  const cropWidth = cropData.normalizedCropRect.width * scaledImageWidth;
  const cropHeight = cropData.normalizedCropRect.height * scaledImageHeight;

  // Set new canvas dimensions
  canvas.setDimensions({ width: targetWidth, height: targetHeight });

  // Use uniform scaling to maintain aspect ratio
  const scale = Math.min(targetWidth / cropWidth, targetHeight / cropHeight);

  // Create viewport transform with uniform scaling
  const vpt: [number, number, number, number, number, number] = [
    scale,
    0,
    0,
    scale,
    -cropLeft * scale,
    -cropTop * scale,
  ];

  canvas.setViewportTransform(vpt);
  canvas.renderAll();

  // Update crop data with new container dimensions
  if (setCropData) {
    const updatedCropData = {
      ...cropData,
      canvasDimensions: {
        width: containerRect.width,
        height: containerRect.height,
      },
    };
    setCropData(updatedCropData);
  }
};

const handleOriginalCanvasResize = (canvas: Canvas, container: HTMLElement) => {
  const containerRect = container.getBoundingClientRect();
  const currentWidth = canvas.getWidth();
  const currentHeight = canvas.getHeight();

  const { width: fittedWidth, height: fittedHeight } = calculateFittedCanvasDimensions(
    currentWidth,
    currentHeight,
    containerRect.width,
    containerRect.height
  );

  const { width: targetWidth, height: targetHeight } = applyMinimumDimensions(
    fittedWidth,
    fittedHeight
  );

  // Only resize if target dimensions differ significantly from current
  if (!shouldResize(currentWidth, currentHeight, targetWidth, targetHeight)) {
    return;
  }

  const scaleX = targetWidth / currentWidth;
  const scaleY = targetHeight / currentHeight;
  const imageScale = Math.min(scaleX, scaleY);
  const actualWidth = currentWidth * imageScale;
  const actualHeight = currentHeight * imageScale;

  canvas.setDimensions({ width: actualWidth, height: actualHeight });
  canvas.renderOnAddRemove = false;

  scaleCanvasObjects(canvas, imageScale);

  const bgImg = canvas.backgroundImage;
  if (bgImg) {
    positionBackgroundImage(bgImg, actualWidth, actualHeight, imageScale);
  }

  canvas.renderOnAddRemove = true;
  canvas.requestRenderAll();
};

export const useResponsiveCanvas = ({
  fabricCanvas,
  containerRef,
  cropData,
  setCropData,
}: UseResponsiveCanvasProps) => {
  const resizeObserverRef = useRef<ResizeObserver | null>(null);

  const resizeCanvas = useCallback(() => {
    if (!fabricCanvas?.current || !containerRef.current) return;

    const canvas = fabricCanvas.current;

    if (cropData?.isCropped && cropData.normalizedCropRect && cropData.canvasDimensions) {
      handleCroppedCanvasResize(canvas, containerRef.current, cropData, setCropData);
    } else {
      handleOriginalCanvasResize(canvas, containerRef.current);
    }
  }, [fabricCanvas, containerRef, cropData, setCropData]);

  useEffect(() => {
    if (!containerRef.current) return;

    const container = containerRef.current;
    resizeObserverRef.current = new ResizeObserver(() => {
      resizeCanvas();
    });

    resizeObserverRef.current.observe(container);

    return () => {
      resizeObserverRef.current?.disconnect();
      resizeObserverRef.current = null;
    };
  }, [containerRef, resizeCanvas]);

  return {
    resizeCanvas,
  };
};
