import { ToolMode } from "@/shared/types";
import { Canvas } from "fabric";
import { adjustStrokeWidthForViewport } from "../operations/annotations";

export const getToolConfig = (mode: ToolMode, canvas?: Canvas) => {
  // Helper function to get adjusted stroke width
  const getAdjustedStrokeWidth = (defaultWidth: number) => {
    return canvas ? adjustStrokeWidthForViewport(canvas, defaultWidth) : defaultWidth;
  };

  switch (mode) {
    case "freehand":
      return {
        color: "red",
        width: getAdjustedStrokeWidth(3),
        strokeUniform: true,
      };
    case "text":
      return {
        fontSize: 16,
        fill: "red",
        width: 200,
        selectable: false,
        evented: false,
        strokeUniform: true,
      };
    case "rect":
      return {
        width: 1,
        height: 1,
        fill: "transparent",
        stroke: "red",
        strokeWidth: getAdjustedStrokeWidth(2),
        strokeUniform: true,
        selectable: false,
        evented: false,
      };
    case "circle":
      return {
        radius: 1,
        fill: "transparent",
        stroke: "red",
        strokeWidth: getAdjustedStrokeWidth(2),
        selectable: false,
        evented: false,
        strokeUniform: true,
      };
    case "line":
      return {
        stroke: "red",
        strokeWidth: getAdjustedStrokeWidth(2),
        selectable: false,
        evented: false,
        strokeUniform: true,
      };
    case "crop":
      return {
        width: 1,
        height: 1,
        fill: "rgba(255, 255, 255, .1)",
        stroke: "#ff0000",
        strokeWidth: getAdjustedStrokeWidth(0.5),
        strokeDashArray: [5, 5],
        selectable: false,
        evented: false,
        strokeUniform: true,
        name: "cropRect",
      };
    case "measure":
      return {
        strokeWidth: getAdjustedStrokeWidth(2),
        fill: "#ff6b35",
        stroke: "#ff6b35",
        originX: "center",
        originY: "center",
        selectable: false,
        evented: false,
        hasControls: false,
        hasBorders: false,
        lockScalingFlip: true,
        name: "measurementLine",
        strokeUniform: true,
      };
    default:
      return {};
  }
};

export const constrainToCanvas = (pointer: { x: number; y: number }, canvas: Canvas) => {
  const canvasWidth = canvas.getWidth();
  const canvasHeight = canvas.getHeight();

  return {
    x: Math.max(0, Math.min(pointer.x, canvasWidth - 1)),
    y: Math.max(0, Math.min(pointer.y, canvasHeight - 1)),
  };
};

export const transformPointer = (
  pointer: { x: number; y: number },
  canvas: Canvas
): { x: number; y: number } => {
  const vpt = canvas.viewportTransform;
  const needsTransform = vpt && (vpt[0] !== 1 || vpt[3] !== 1 || vpt[4] !== 0 || vpt[5] !== 0);

  if (needsTransform) {
    return {
      x: (pointer.x - vpt[4]) / vpt[0],
      y: (pointer.y - vpt[5]) / vpt[3],
    };
  }

  return pointer;
};
